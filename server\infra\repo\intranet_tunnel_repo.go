package repo

import "time"

const (
	IntranetTunnelTableName = "gateway_intranet_tunnel"
)

type IntranetTunnel struct {
	ID            int       `gorm:"primaryKey;autoIncrement:true;column:id;comment:主键。"`
	Name          string    `gorm:"not null;column:name;comment:隧道名称。"`
	ClientUUID    string    `gorm:"column:clientid;comment:客户端ID。"`
	ClientName    string    `gorm:"column:clientname;comment:客户端名称。"`
	ClientIp      string    `gorm:"column:clientip;comment:客户端IP。"`
	Protocol      string    `gorm:"column:protocol;comment:转发协议类型。"`
	ServerPort    int       `gorm:"column:serverport;comment:服务器端口。"`
	ClientPort    string    `gorm:"column:clientport;comment:客户端端口。"`
	Enable        bool      `gorm:"column:enable;comment:是否启用。"`
	Description   string    `gorm:"column:description;comment:描述信息。"`
	Encryption    bool      `gorm:"column:encryption;comment:是否加密。"`
	Password      string    `gorm:"column:password;comment:密码。"`
	RateLimit     int       `gorm:"column:ratelimit;comment:限流(MB)。"`
	CreateTime    time.Time `gorm:"column:createtime;comment:创建时间。"`
	Connected     time.Time `gorm:"column:lastconnectiontime;comment:最后连接时间。"`
	Online        bool      `gorm:"column:online;comment:是否在线。"`
	ClientType    string    `gorm:"column:clienttype;comment:客户端类型。"`
	ClientGroup   string    `gorm:"column:clientgroup;comment:客户端所在集群。"`
	ServiceName   string    `gorm:"column:servicename;comment:服务名称。"`
	ClientRoute   string    `gorm:"column:clientroute;comment:客户端路由地址。"`         // 客户端的路由信息，用于URL代理
	ServerRoute   string    `gorm:"column:serverroute;comment:服务器路由地址。"`         // 服务端的路由信息，用于URL代理
	RelayVersion  string    `gorm:"column:tunnelversion;comment:代理客户端版本。"`       // Relay版本
	ClientVersion string    `gorm:"column:osclientversion;comment:BaseOS客户端版本。"` // 客户端版本
}

// TableName 指定表名
func (IntranetTunnel) TableName() string {
	return IntranetTunnelTableName
}
