package service

import (
	"time"

	"socks/server/monitor/domain/entity"
	"socks/server/monitor/domain/service"
	"socks/server/monitor/infra/dao"
)

// MonitorAppService 监控应用服务接口
type MonitorAppService interface {
	// CreateHTTPMonitor 创建HTTP监控记录
	CreateHTTPMonitor(client *entity.ClientInfo, serverRoute, serviceName string) error

	// CreateTCPMonitor 创建TCP监控记录
	CreateTCPMonitor(client *entity.ClientInfo, serverPort int, serviceName string) error

	// RecordHTTPRequest 记录HTTP请求
	RecordHTTPRequest(clientUUID, serverRoute string, requestCount int64) error

	// RecordTCPTraffic 记录TCP流量
	RecordTCPTraffic(clientUUID string, serverPort int, bytesIn, bytesOut int64) error

	// GetMonitorByHTTPRoute 根据HTTP路由获取监控记录
	GetMonitorByHTTPRoute(clientUUID, serverRoute string) (*entity.TunnelMonitor, error)

	// GetMonitorByTCPPort 根据TCP端口获取监控记录
	GetMonitorByTCPPort(clientUUID string, serverPort int) (*entity.TunnelMonitor, error)

	// GetMonitorsByClient 根据客户端UUID获取所有监控记录
	GetMonitorsByClient(clientUUID string) ([]*entity.TunnelMonitor, error)

	// GetMonitorsByGroup 根据客户端组获取所有监控记录
	GetMonitorsByGroup(clientGroup string) ([]*entity.TunnelMonitor, error)

	// GetTrafficDataInRange 获取指定时间范围内的流量数据
	GetTrafficDataInRange(clientUUID string, start, end time.Time) ([]*entity.TunnelMonitor, error)

	// GetActiveMonitors 获取活跃的监控记录
	GetActiveMonitors(since time.Time) ([]*entity.TunnelMonitor, error)

	// DeleteMonitorsByClient 删除指定客户端的所有监控记录
	DeleteMonitorsByClient(clientUUID string) error

	// GetAllMonitors 获取所有监控记录
	GetAllMonitors() ([]*entity.TunnelMonitor, error)

	// GetTrafficSummary 获取流量汇总信息
	GetTrafficSummary(clientUUID string, hours int) (*entity.TrafficSummary, error)

	// StartCleanupTask 启动定期清理任务
	StartCleanupTask()
}

// monitorAppServiceImpl 监控应用服务实现
type monitorAppServiceImpl struct {
	domainService *service.MonitorService
}

// GetMonitorAppService 获取监控应用服务实例
func GetMonitorAppService() MonitorAppService {
	repository := dao.GetMonitorDaoImpl()
	domainService := service.GetMonitorService(repository)

	return &monitorAppServiceImpl{
		domainService: domainService,
	}
}

// CreateHTTPMonitor 创建HTTP监控记录
func (mas *monitorAppServiceImpl) CreateHTTPMonitor(client *entity.ClientInfo, serverRoute, serviceName string) error {
	return mas.domainService.CreateHTTPMonitor(client, serverRoute, serviceName)
}

// CreateTCPMonitor 创建TCP监控记录
func (mas *monitorAppServiceImpl) CreateTCPMonitor(client *entity.ClientInfo, serverPort int, serviceName string) error {
	return mas.domainService.CreateTCPMonitor(client, serverPort, serviceName)
}

// RecordHTTPRequest 记录HTTP请求
func (mas *monitorAppServiceImpl) RecordHTTPRequest(clientUUID, serverRoute string, requestCount int64) error {
	return mas.domainService.RecordHTTPRequest(clientUUID, serverRoute, requestCount)
}

// RecordTCPTraffic 记录TCP流量
func (mas *monitorAppServiceImpl) RecordTCPTraffic(clientUUID string, serverPort int, bytesIn, bytesOut int64) error {
	return mas.domainService.RecordTCPTraffic(clientUUID, serverPort, bytesIn, bytesOut)
}

// GetMonitorByHTTPRoute 根据HTTP路由获取监控记录
func (mas *monitorAppServiceImpl) GetMonitorByHTTPRoute(clientUUID, serverRoute string) (*entity.TunnelMonitor, error) {
	return mas.domainService.GetMonitorByHTTPRoute(clientUUID, serverRoute)
}

// GetMonitorByTCPPort 根据TCP端口获取监控记录
func (mas *monitorAppServiceImpl) GetMonitorByTCPPort(clientUUID string, serverPort int) (*entity.TunnelMonitor, error) {
	return mas.domainService.GetMonitorByTCPPort(clientUUID, serverPort)
}

// GetMonitorsByClient 根据客户端UUID获取所有监控记录
func (mas *monitorAppServiceImpl) GetMonitorsByClient(clientUUID string) ([]*entity.TunnelMonitor, error) {
	return mas.domainService.GetMonitorsByClient(clientUUID)
}

// GetMonitorsByGroup 根据客户端组获取所有监控记录
func (mas *monitorAppServiceImpl) GetMonitorsByGroup(clientGroup string) ([]*entity.TunnelMonitor, error) {
	return mas.domainService.GetMonitorsByGroup(clientGroup)
}

// GetTrafficDataInRange 获取指定时间范围内的流量数据
func (mas *monitorAppServiceImpl) GetTrafficDataInRange(clientUUID string, start, end time.Time) ([]*entity.TunnelMonitor, error) {
	return mas.domainService.GetTrafficDataInRange(clientUUID, start, end)
}

// GetActiveMonitors 获取活跃的监控记录
func (mas *monitorAppServiceImpl) GetActiveMonitors(since time.Time) ([]*entity.TunnelMonitor, error) {
	return mas.domainService.GetActiveMonitors(since)
}

// DeleteMonitorsByClient 删除指定客户端的所有监控记录
func (mas *monitorAppServiceImpl) DeleteMonitorsByClient(clientUUID string) error {
	return mas.domainService.DeleteMonitorsByClient(clientUUID)
}

// GetAllMonitors 获取所有监控记录
func (mas *monitorAppServiceImpl) GetAllMonitors() ([]*entity.TunnelMonitor, error) {
	return mas.domainService.GetAllMonitors()
}

// GetTrafficSummary 获取流量汇总信息
func (mas *monitorAppServiceImpl) GetTrafficSummary(clientUUID string, hours int) (*entity.TrafficSummary, error) {
	return mas.domainService.GetTrafficSummary(clientUUID, hours)
}

// StartCleanupTask 启动定期清理任务
func (mas *monitorAppServiceImpl) StartCleanupTask() {
	mas.domainService.StartCleanupTask()
}
