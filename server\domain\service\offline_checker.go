package service

import (
	"log"
	"sync"
	"time"

	"socks/server/domain/entity"
	"socks/server/domain/event"
	"socks/server/util"
)

var (
	clientOfflineChecker *ClientOfflineChecker
	cocOnce              sync.Once
)

// ClientOfflineChecker 每小时检查一次所有注册的客户端是否有对应tunnel
// 如果不存在tunnel，则记录一次掉线（滚动记录最近30天）
type ClientOfflineChecker struct {
	getAllClients func() map[string]*entity.Client
	getTunnel     func(uuid string) *entity.SafeConn
	conn          *event.Connection

	ticker   *time.Ticker
	stopChan chan struct{}
	running  bool
	mutex    sync.Mutex
}

// GetClientOfflineChecker 获取（或创建）掉线检查器
func GetClientOfflineChecker(config *util.TunnelConfig) *ClientOfflineChecker {
	cocOnce.Do(func() {
		conn := event.GetConnection(config)
		clientOfflineChecker = &ClientOfflineChecker{
			conn:          conn,
			getAllClients: conn.GetAllClients,
			getTunnel:     conn.GetTunnel,
			stop<PERSON>han:      make(chan struct{}, 1),
		}
	})
	return clientOfflineChecker
}

// Start 启动每小时检查
func (c *ClientOfflineChecker) Start() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	if c.running {
		return
	}
	c.ticker = time.NewTicker(1 * time.Hour)
	c.running = true
	go c.loop()
	log.Printf("client offline checker started: interval=%v", time.Hour)
}

// Stop 停止检查
func (c *ClientOfflineChecker) Stop() {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	if !c.running {
		return
	}
	c.running = false
	if c.ticker != nil {
		c.ticker.Stop()
	}
	select {
	case c.stopChan <- struct{}{}:
	default:
	}
	log.Printf("client offline checker stopped")
}

func (c *ClientOfflineChecker) loop() {
	for {
		select {
		case <-c.ticker.C:
			c.performCheck()
		case <-c.stopChan:
			return
		}
	}
}

// performCheck 立即执行一次检查（便于测试调用）
func (c *ClientOfflineChecker) performCheck() {
	clients := c.getAllClients()
	now := time.Now()
	for _, cli := range clients {
		// 判断是否有tunnel
		online := c.getTunnel(cli.UUID) != nil
		if online {
			continue
		}
		cli.RecordHourlyStatus(now)
		c.conn.ClientOffline(cli.UUID)
	}
}
