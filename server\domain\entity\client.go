package entity

import (
	"sync"
	"time"
)

type Client struct {
	Name          string // 客户端名称
	IP            string // 客户端IP地址
	UUID          string // 客户端唯一id
	Type          string // 端口对应的代理
	Group         string // 代理客户端所在集群组别
	RelayVersion  string // Relay版本
	ClientVersion string // 客户端版本（从环境变量BOS_Client_Version获取）
	Online        bool   // 是否在线

	// 最近30天掉线统计（按天聚合，每小时检查一次），仅记录每日离线次数
	offlineDaily  [30]uint8  // 每天的离线次数（小时粒度聚合，理论上0..24）
	dayIndex      int        // 指向当前天的数据槽位
	mu            sync.Mutex // 并发保护
	lastRecordDay time.Time  // 上一次记录的日期（去除时分秒）
}

// RecordHourlyStatus 每小时检查一次，将当日的离线次数累加，并维护最近30天的滚动窗口（仅记录每日次数，不记录总和）
func (c *Client) RecordHourlyStatus(now time.Time) {
	c.mu.Lock()
	defer c.mu.Unlock()

	dayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())

	if c.lastRecordDay.IsZero() {
		c.lastRecordDay = dayStart
	}

	if dayStart.After(c.lastRecordDay) {
		// 计算跨越了多少天
		daysDiff := int(dayStart.Sub(c.lastRecordDay).Hours() / 24)
		if daysDiff >= len(c.offlineDaily) {
			// 超过窗口，等价于清空统计
			for i := range c.offlineDaily {
				c.offlineDaily[i] = 0
			}
			c.dayIndex = 0
		} else if daysDiff > 0 {
			for i := 0; i < daysDiff; i++ {
				// 向前推进一天，淘汰最老的一天
				c.dayIndex = (c.dayIndex + 1) % len(c.offlineDaily)
				c.offlineDaily[c.dayIndex] = 0
			}
		}
		c.lastRecordDay = dayStart
	}

	// 聚合到当日
	c.offlineDaily[c.dayIndex]++

}

// GetOfflineDailyCounts 获取最近30天（按天聚合）的离线次数，返回从最旧到最新的顺序
func (c *Client) GetOfflineDailyCounts() []int {
	c.mu.Lock()
	defer c.mu.Unlock()
	res := make([]int, len(c.offlineDaily))
	for i := 0; i < len(c.offlineDaily); i++ {
		idx := (c.dayIndex + 1 + i) % len(c.offlineDaily)
		res[i] = int(c.offlineDaily[idx])
	}
	return res
}

func (c *Client) GetTunnelName(port string) string {
	return c.Name + ":" + port
}
