package entity

// ClientInfo 客户端信息实体
type ClientInfo struct {
	UUID  string `json:"uuid"`  // 客户端UUID
	IP    string `json:"ip"`    // 客户端IP
	Name  string `json:"name"`  // 客户端名称
	Group string `json:"group"` // 客户端组
}

// NewClientInfo 创建客户端信息实体
func NewClientInfo(uuid, ip, name, group string) *ClientInfo {
	return &ClientInfo{
		UUID:  uuid,
		IP:    ip,
		Name:  name,
		Group: group,
	}
}

// IsValid 验证客户端信息是否有效
func (c *ClientInfo) IsValid() bool {
	return c.UUID != "" && c.IP != "" && c.Name != ""
}
