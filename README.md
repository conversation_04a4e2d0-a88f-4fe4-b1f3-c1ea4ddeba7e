# SDN 隧道网关

一个基于 Go 语言开发的企业级内网穿透解决方案，支持 HTTP/HTTPS 协议代理和 TCP 端口代理两种模式。

## 项目概述

SDN 隧道网关是一个客户端-服务端架构的网络代理系统，专为解决内网服务对外暴露、跨网络访问和统一流量管理而设计。通过建立安全隧道实现内网服务的外网访问，支持高性能的 TCP 直连转发和完整的连接管理。

### 主要特性

- **双模式代理**：支持 HTTP/HTTPS URL 路径代理和 TCP 端口代理
- **高性能转发**：采用 TCP 直连转发，支持大文件传输和流式服务
- **智能重连**：客户端自动检测连接断开并重连，保证服务稳定性
- **流量监控**：按客户端和服务维度统计请求量和流量数据
- **持久化存储**：支持映射关系的持久化和启动时自动恢复
- **Windows 服务**：服务端支持 Windows 服务模式运行

## 系统架构

```
外部请求者 ←→ 网关服务端 ←→ 内网客户端 ←→ 本地服务
          (公网)      (隧道连接)    (内网)
```

### 核心组件

#### 网关服务端 (TunnelGateway.exe)
- **HTTP 管理面**：提供客户端注册、端口分配、URL 映射等管理接口
- **TCP 代理入口**：直接监听 TCP 端口，解析 HTTP/HTTPS 请求并转发
- **隧道管理器**：维护客户端控制连接和数据通道
- **流量监控**：实时统计和监控流量数据
- **持久化存储**：支持 SQLite 数据库存储

#### 内网客户端 (BaseOS.Desktop.TunnelGateway.exe)
- **连接管理**：维护与服务端的长连接，支持自动重连
- **服务注册**：注册本地服务的访问映射
- **数据转发**：建立数据通道，转发请求和响应
- **配置管理**：支持 JSON 配置文件和命令行参数

## 快速开始

### 环境要求

- Go 1.19 或更高版本
- Windows 操作系统（服务端）
- Windows/Linux 操作系统（客户端）

### 构建项目

使用提供的构建脚本：

```bash
# Linux/macOS
bash build.sh

# Windows
build.sh
```

构建完成后，可执行文件将生成在 `build/` 目录下：
- `TunnelGateway.exe` - 服务端程序
- `BaseOS.Desktop.TunnelGateway.exe` - 客户端程序

### 服务端部署

#### 1. 准备配置文件

创建服务端配置文件 `Gateway.json`：

```json
{
  "IntranetTunnelManagerPort": 8080,
  "IntranetTunnelURLProxyPort": 8081,
  "IntranetTunnelMinPort": 9000,
  "IntranetTunnelMaxPort": 9999,
  "IntranetTunnelTimeout": 30,
  "IntranetTunnelMaxConnection": 1000,
  "IntranetTunnelSlidingExpiration": 7,
  "GatewayDBType": "sqlite3",
  "GatewayConnection": "gateway.db"
}
```

#### 2. 运行服务端

```bash
# 直接运行（开发模式）
TunnelGateway.exe -config Gateway.json

# 安装为 Windows 服务
TunnelGateway.exe -install -config Gateway.json

# 启动服务
TunnelGateway.exe -start

# 停止服务
TunnelGateway.exe -stop

# 卸载服务
TunnelGateway.exe -uninstall
```

### 客户端部署

#### 1. 准备配置文件

创建客户端配置文件 `BaseOS.Desktop.TunnelGateway.json`：

```json
{
  "Server_ip": "*************",
  "Server_port": 8080,
  "Local_host": "127.0.0.1",
  "Manager_port": 8082,
  "Type": "desktop",
  "Group": "development",
  "Name": "dev-client-01",
  "Tunnels": [
    {
      "service_name": "web-service",
      "service_group": "web",
      "service_port": "3000",
      "base_url": "/api/web",
      "api_type": "http"
    }
  ]
}
```

#### 2. 运行客户端

```bash
# 使用配置文件运行
BaseOS.Desktop.TunnelGateway.exe -config BaseOS.Desktop.TunnelGateway.json

# 使用命令行参数运行
BaseOS.Desktop.TunnelGateway.exe -server ************* -port 8080 -name dev-client-01
```

## 使用说明

### HTTP/HTTPS 代理

1. 客户端注册 URL 映射后，外部用户可通过服务端的代理端口访问内网服务
2. 访问格式：`http://服务端IP:URLProxyPort/注册的BaseURL路径`
3. 服务端会自动将请求转发到对应的内网客户端服务

### TCP 端口代理

1. 客户端可申请端口映射，服务端会分配一个公网端口
2. 外部用户直接连接分配的端口即可访问内网服务
3. 支持任意 TCP 协议的服务代理

### 管理接口

服务端提供 RESTful API 进行管理：

- `GET /clients/groups` - 查看客户端分组信息
- `GET /clients/filter` - 筛选客户端
- `POST /tunnel/refresh` - 刷新隧道状态

## 测试

项目提供了完整的测试套件：

```bash
# 运行基础测试
cd test
bash run_tests_simple.sh

# 运行完整测试（需要 CGO 支持）
bash run_tests.sh

# 运行特定测试
go test -v -run TestTunnelIntegration
```

## 配置参数说明

### 服务端配置参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `IntranetTunnelManagerPort` | HTTP 管理接口端口 | 8080 |
| `IntranetTunnelURLProxyPort` | URL 代理服务端口 | 8081 |
| `IntranetTunnelMinPort` | 端口代理最小端口号 | 9000 |
| `IntranetTunnelMaxPort` | 端口代理最大端口号 | 9999 |
| `IntranetTunnelTimeout` | 连接超时时间（秒） | 30 |
| `IntranetTunnelMaxConnection` | 最大连接数 | 1000 |
| `IntranetTunnelSlidingExpiration` | 缓存过期时间（天） | 7 |
| `GatewayDBType` | 数据库类型 | sqlite3 |
| `GatewayConnection` | 数据库连接字符串 | gateway.db |

### 客户端配置参数

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `Server_ip` | 服务端 IP 地址 | ************* |
| `Server_port` | 服务端管理端口 | 8080 |
| `Local_host` | 本地服务 IP | 127.0.0.1 |
| `Manager_port` | 客户端 API 端口 | 8082 |
| `Type` | 客户端类型 | desktop |
| `Group` | 客户端分组 | development |
| `Name` | 客户端名称 | dev-client-01 |

### 隧道配置参数

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `service_name` | 服务名称 | web-service |
| `service_group` | 服务分组 | web |
| `service_port` | 本地服务端口 | 3000 |
| `base_url` | URL 路径前缀 | /api/web |
| `api_type` | API 类型 | http |

## 故障排除

### 常见问题

#### 1. 客户端无法连接到服务端

**症状**：客户端启动后显示连接失败

**解决方案**：
- 检查服务端是否正常运行
- 确认网络连通性和防火墙设置
- 验证服务端 IP 和端口配置是否正确

#### 2. URL 代理访问 404

**症状**：通过代理 URL 访问返回 404 错误

**解决方案**：
- 确认客户端已成功注册 URL 映射
- 检查 BaseURL 路径配置是否正确
- 验证本地服务是否正常运行

#### 3. 端口代理连接被拒绝

**症状**：连接分配的端口时被拒绝

**解决方案**：
- 确认端口已成功分配
- 检查服务端端口范围配置
- 验证客户端与本地服务的连接

### 日志查看

#### 服务端日志
- Windows 服务模式：查看 `logs/TunnelGateway.log`
- 控制台模式：直接在控制台输出

#### 客户端日志
- 直接在控制台输出，包含连接状态和错误信息

## 性能优化

### 服务端优化

1. **调整连接池大小**：根据并发需求调整 `IntranetTunnelMaxConnection`
2. **优化数据库配置**：使用合适的数据库连接池设置
3. **监控资源使用**：定期检查 CPU 和内存使用情况

### 客户端优化

1. **网络延迟优化**：选择网络延迟较低的服务端
2. **本地服务优化**：确保本地服务响应时间合理
3. **连接复用**：利用 HTTP keep-alive 减少连接开销

## 安全建议

1. **网络隔离**：将服务端部署在安全的网络环境中
2. **访问控制**：限制管理接口的访问权限
3. **日志审计**：定期审查访问日志和异常记录
4. **版本更新**：及时更新到最新版本以获得安全修复

## 文档链接

### 用户文档
- [快速入门指南](docs/quick-start-zh.md) - 5分钟快速部署指南
- [详细使用手册](docs/user-manual-zh.md) - 完整的安装配置和使用说明

### 技术文档
- [产品规格说明](docs/product-specification.md) - 详细的产品功能说明
- [软件设计文档](docs/software-design-document.md) - 技术架构和设计细节
- [版本数据库集成](docs/version_database_integration.md) - 数据库集成说明

## 许可证

本项目采用企业内部许可证，仅供内部使用。

## 贡献

如需贡献代码或报告问题，请联系项目维护团队。