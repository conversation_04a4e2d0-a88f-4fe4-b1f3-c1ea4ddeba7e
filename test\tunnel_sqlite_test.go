package test

import (
	"os"
	"path/filepath"
	"testing"

	"socks/server/util"
)

// setupSQLiteTestEnvironment 设置 SQLite 测试环境
func setupSQLiteTestEnvironment(t *testing.T) *TestConfig {
	// 创建临时测试目录
	testDir, err := os.MkdirTemp("", "tunnel_sqlite_test_*")
	if err != nil {
		t.Fatalf("Failed to create test directory: %v", err)
	}

	// 创建 SQLite 数据库文件路径
	dbPath := filepath.Join(testDir, "test.db")

	// 创建服务端配置
	serverConfig := &util.TunnelConfig{
		ManagerPort:       48080,
		URLProxyPort:      48081,
		MinPort:           49000,
		MaxPort:           49100,
		Timeout:           30,
		MaxConnection:     100,
		SlidingExpiration: 1, // 1天
		DbType:            "sqlite3",
		DbConnectCommand:  dbPath,
	}

	// 创建客户端配置
	clientConfig := &ClientConfig{
		ServerIP:   "127.0.0.1",
		ServerPort: 48080,
		LocalHost:  "127.0.0.1",
		APIPort:    48082,
		Type:       "test",
		Group:      "test-group",
		HostName:   "test-client-sqlite",
		Tunnels: []Tunnel{
			{
				AppName:      "test-app-sqlite",
				BaseURL:      "/api/v2",
				ServiceName:  "test-service-sqlite",
				ServiceGroup: "test-group",
				ServicePort:  "8000",
				APIType:      "HTTP",
			},
			{
				ServiceName: "tcp-service-sqlite",
				ServicePort: "8001",
				APIType:     "TCP",
			},
		},
	}

	return &TestConfig{
		ServerConfig: serverConfig,
		ClientConfig: clientConfig,
		TestDataDir:  testDir,
	}
}

// TestTunnelIntegrationWithSQLite 使用 SQLite3 的集成测试
func TestTunnelIntegrationWithSQLite(t *testing.T) {
	// 设置测试环境
	config := setupSQLiteTestEnvironment(t)
	defer cleanupTestEnvironment(config)

	// 启动服务器
	cancel, wg := startTestServer(t, config)
	defer func() {
		cancel()
		wg.Wait()
	}()

	// 运行子测试
	t.Run("SQLiteConfigLoading", func(t *testing.T) {
		testSQLiteConfigLoading(t, config)
	})

	t.Run("SQLiteDatabaseConnection", func(t *testing.T) {
		testSQLiteDatabaseConnection(t, config)
	})

	t.Run("SQLiteDataPersistence", func(t *testing.T) {
		testSQLiteDataPersistence(t, config)
	})
}

// testSQLiteConfigLoading 测试 SQLite 配置加载
func testSQLiteConfigLoading(t *testing.T, config *TestConfig) {
	t.Log("Testing SQLite configuration loading...")

	// 验证服务端配置
	if config.ServerConfig.ManagerPort != 48080 {
		t.Errorf("Expected ManagerPort 48080, got %d", config.ServerConfig.ManagerPort)
	}

	if config.ServerConfig.DbType != "sqlite3" {
		t.Errorf("Expected DbType sqlite3, got %s", config.ServerConfig.DbType)
	}

	if config.ServerConfig.DbConnectCommand == "" {
		t.Error("Expected non-empty DbConnectCommand")
	}

	// 验证数据库文件路径
	if !filepath.IsAbs(config.ServerConfig.DbConnectCommand) {
		t.Error("Expected absolute path for SQLite database")
	}

	t.Log("SQLite configuration loading test passed")
}

// testSQLiteDatabaseConnection 测试 SQLite 数据库连接
func testSQLiteDatabaseConnection(t *testing.T, config *TestConfig) {
	t.Log("Testing SQLite database connection...")

	// 重新初始化数据库连接以确保使用测试配置
	db, err := util.ConnectGormDB(config.ServerConfig)
	if err != nil {
		t.Fatalf("Failed to connect to SQLite database: %v", err)
	}

	if db == nil {
		t.Fatal("SQLite database connection is nil")
	}

	// 测试数据库连接
	sqlDB, err := db.DB()
	if err != nil {
		t.Fatalf("Failed to get underlying SQLite database connection: %v", err)
	}

	err = sqlDB.Ping()
	if err != nil {
		t.Fatalf("SQLite database ping failed: %v", err)
	}

	// 验证数据库文件是否创建
	if _, err := os.Stat(config.ServerConfig.DbConnectCommand); os.IsNotExist(err) {
		t.Error("SQLite database file was not created")
	}

	t.Log("SQLite database connection test passed")
}

// testSQLiteDataPersistence 测试 SQLite 数据持久化
func testSQLiteDataPersistence(t *testing.T, config *TestConfig) {
	t.Log("Testing SQLite data persistence...")

	// 获取数据库连接
	db, err := util.ConnectGormDB(config.ServerConfig)
	if err != nil {
		t.Fatalf("Failed to connect to SQLite database: %v", err)
	}

	// 检查表是否自动创建
	var tableCount int64
	err = db.Raw("SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name LIKE 'gateway_%'").Scan(&tableCount).Error
	if err != nil {
		t.Fatalf("Failed to query SQLite tables: %v", err)
	}

	t.Logf("Found %d gateway tables in SQLite database", tableCount)

	// 验证数据库文件大小（应该大于0）
	fileInfo, err := os.Stat(config.ServerConfig.DbConnectCommand)
	if err != nil {
		t.Fatalf("Failed to get SQLite database file info: %v", err)
	}

	if fileInfo.Size() == 0 {
		t.Error("SQLite database file is empty")
	}

	t.Logf("SQLite database file size: %d bytes", fileInfo.Size())
	t.Log("SQLite data persistence test passed")
}
