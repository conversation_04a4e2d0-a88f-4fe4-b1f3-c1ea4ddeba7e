package service

import (
	"net"
	"socks/server/application/event"
	"socks/server/domain/entity"
	"socks/server/util"
)

type PortProxyService interface {
	RegisterClient(client *entity.Client, conn net.Conn) error
	AllocateServerPort(clientUUID, ipPort, serviceName, protocol string) (int, error)
	Start(clientUUID string, ipPort string, serverPort int) error
	Stop(serverPort int) error
	RecoverFromDB() error
	UpdateTunnelConfig(ids []int, all bool) error
	FilterClients(name, ip, uuid, clientType, group, relayVersion, clientVersion string) []*entity.Client
	GetClientsByGroup() map[string][]*entity.Client
	GetClientURLPath(clientUUID string) []string
}

func GetPortProxyService(config *util.TunnelConfig) PortProxyService {
	return event.GetPortProxy(config)
}
