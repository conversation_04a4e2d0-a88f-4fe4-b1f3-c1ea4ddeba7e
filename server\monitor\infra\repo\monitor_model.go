package repo

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"socks/server/monitor/domain/entity"

	"gorm.io/gorm"
)

const (
	TunnelMonitorTableName = "gateway_intranet_tunnel_monitor"
)

// TrafficDataModel 时序流量数据模型（用于数据库存储）
type TrafficDataModel struct {
	Timestamp    time.Time `json:"timestamp"`
	RequestCount int64     `json:"request_count"`
	BytesIn      int64     `json:"bytes_in"`
	BytesOut     int64     `json:"bytes_out"`
}

// Value 实现 driver.Valuer 接口，用于数据库存储
func (t TrafficDataModel) Value() (driver.Value, error) {
	bytes, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (t *TrafficDataModel) Scan(value interface{}) error {
	if value == nil {
		*t = TrafficDataModel{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return nil
	}

	return json.Unmarshal(bytes, t)
}

// ToEntity 转换为实体
func (t *TrafficDataModel) ToEntity() *entity.TrafficData {
	return &entity.TrafficData{
		Timestamp:    t.Timestamp,
		RequestCount: t.RequestCount,
		BytesIn:      t.BytesIn,
		BytesOut:     t.BytesOut,
	}
}

// FromEntity 从实体创建
func (t *TrafficDataModel) FromEntity(data *entity.TrafficData) {
	t.Timestamp = data.Timestamp
	t.RequestCount = data.RequestCount
	t.BytesIn = data.BytesIn
	t.BytesOut = data.BytesOut
}

// TrafficDataListModel 流量数据列表模型
type TrafficDataListModel []TrafficDataModel

// Value 实现 driver.Valuer 接口，用于数据库存储
func (t TrafficDataListModel) Value() (driver.Value, error) {
	if len(t) == 0 {
		return "[]", nil
	}
	bytes, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

// Scan 实现 sql.Scanner 接口，用于数据库读取
func (t *TrafficDataListModel) Scan(value interface{}) error {
	if value == nil {
		*t = TrafficDataListModel{}
		return nil
	}

	var bytes []byte
	switch v := value.(type) {
	case []byte:
		bytes = v
	case string:
		bytes = []byte(v)
	default:
		return nil
	}

	return json.Unmarshal(bytes, t)
}

// ToEntity 转换为实体
func (t TrafficDataListModel) ToEntity() entity.TrafficDataList {
	var result entity.TrafficDataList
	for _, model := range t {
		result = append(result, model.ToEntity())
	}
	return result
}

// FromEntity 从实体创建
func (t *TrafficDataListModel) FromEntity(list entity.TrafficDataList) {
	*t = make(TrafficDataListModel, 0, len(list))
	for _, data := range list {
		var model TrafficDataModel
		model.FromEntity(data)
		*t = append(*t, model)
	}
}

// TunnelMonitorModel 代理隧道流量监控数据库模型
type TunnelMonitorModel struct {
	ID          int                  `gorm:"primaryKey;autoIncrement:true;column:id;comment:主键。"`
	ServerRoute string               `gorm:"size:200;column:server_route;index;comment:服务端代理路由（HTTP类型使用）。"`
	ServerPort  int                  `gorm:"column:server_port;index;comment:服务端代理端口（TCP类型使用）。"`
	TrafficData TrafficDataListModel `gorm:"type:text;column:traffic_data;comment:时序流量数据。"`
	ClientGroup string               `gorm:"size:50;column:client_group;index;comment:客户端所在区域。"`
	TunnelType  string               `gorm:"size:10;column:tunnel_type;index;comment:隧道类型。"`
	ClientUUID  string               `gorm:"size:100;column:client_uuid;index;comment:客户端UUID。"`
	ClientIP    string               `gorm:"size:100;column:client_ip;comment:客户端IP。"`
	ClientName  string               `gorm:"size:100;column:client_name;comment:客户端名称。"`
	ServiceName string               `gorm:"size:100;column:service_name;comment:服务名称。"`
	CreateTime  time.Time            `gorm:"column:create_time;comment:创建时间。"`
	UpdateTime  time.Time            `gorm:"column:update_time;comment:更新时间。"`
}

// TableName 指定表名
func (TunnelMonitorModel) TableName() string {
	return TunnelMonitorTableName
}

// BeforeCreate GORM钩子，创建前设置时间
func (g *TunnelMonitorModel) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	g.CreateTime = now
	g.UpdateTime = now
	return nil
}

// BeforeUpdate GORM钩子，更新前设置时间
func (g *TunnelMonitorModel) BeforeUpdate(tx *gorm.DB) error {
	g.UpdateTime = time.Now()
	return nil
}

// ToEntity 转换为实体
func (g *TunnelMonitorModel) ToEntity() *entity.TunnelMonitor {
	return &entity.TunnelMonitor{
		ID:          g.ID,
		ServerRoute: g.ServerRoute,
		ServerPort:  g.ServerPort,
		TrafficData: g.TrafficData.ToEntity(),
		ClientGroup: g.ClientGroup,
		TunnelType:  entity.TunnelType(g.TunnelType),
		ClientUUID:  g.ClientUUID,
		ClientIP:    g.ClientIP,
		ClientName:  g.ClientName,
		ServiceName: g.ServiceName,
		CreateTime:  g.CreateTime,
		UpdateTime:  g.UpdateTime,
	}
}

// FromEntity 从实体创建
func (g *TunnelMonitorModel) FromEntity(monitor *entity.TunnelMonitor) {
	g.ID = monitor.ID
	g.ServerRoute = monitor.ServerRoute
	g.ServerPort = monitor.ServerPort
	g.TrafficData.FromEntity(monitor.TrafficData)
	g.ClientGroup = monitor.ClientGroup
	g.TunnelType = string(monitor.TunnelType)
	g.ClientUUID = monitor.ClientUUID
	g.ClientIP = monitor.ClientIP
	g.ClientName = monitor.ClientName
	g.ServiceName = monitor.ServiceName
	g.CreateTime = monitor.CreateTime
	g.UpdateTime = monitor.UpdateTime
}

// AddTrafficData 添加流量数据点
func (g *TunnelMonitorModel) AddTrafficData(data *entity.TrafficData) {
	entityList := g.TrafficData.ToEntity()
	entityList.Add(data)
	g.TrafficData.FromEntity(entityList)
}
