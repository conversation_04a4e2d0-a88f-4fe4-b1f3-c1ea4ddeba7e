package event

import (
	"io"
	"log"
	"net"

	"socks/relay"
	"socks/server/domain/entity"
	"socks/server/monitor"
)

const (
	ONLINE           = true
	OFFLINE          = false
	PORT_CHAN_PREFIX = "port"
	URL_CHAN_PREFIX  = "chan"
)

// 一个基于服务端端口层面的代理监听对象，包含：
// 1、服务端代理端口监听器
// 2、服务端和客户端的持久化通讯通道
// 3、系统内的conn对象（端口映射表、通道表、监听器表）
type PortProxyTunnel struct {
	clientId   string
	clientPort string
	serverPort int
	listener   *entity.Listener // 监听serverPort的Listener
	conn       *Connection      // 系统内conn对象
	onExit     func(int)        // tunnel退出时的回调函数，参数为serverPort
}

func NewPortProxyTunnel(conn *Connection, serverPort int, ipPort string, clientId string, onExit func(int)) *PortProxyTunnel {
	return &PortProxyTunnel{
		clientId:   clientId,
		clientPort: ipPort,
		serverPort: serverPort,
		listener:   conn.GetListener(serverPort),
		conn:       conn,
		onExit:     onExit,
	}
}

func (p *PortProxyTunnel) StartProxy() {
	p.conn.UpdatePortProxyOnlineStatus(p.clientId, p.clientPort, ONLINE)
	p.conn.UpdateLastConnectionTime(p.clientId, p.clientPort)

	// 确保在函数退出时调用回调函数进行清理
	defer func() {
		if p.onExit != nil {
			p.onExit(p.serverPort)
		}
	}()

	for {
		select {
		case <-p.listener.Stop:
			return
		default:
			// 获取用户进行访问的请求连接
			requestConn, err := p.listener.GetListener().Accept()
			if err != nil {
				log.Printf("accept error on ps=%d: %v", p.serverPort, err)
				if err == io.EOF {
					return
				}
				continue
			}
			go p.StartTransData2RequestClient(requestConn)
		}
	}
}

func (p *PortProxyTunnel) StopProxy() error {
	// 当前端口映射停止传输时，仅需关闭监听对应serverPort端口的listener，无需关闭底层的tcp通道
	p.conn.CloseListener(p.serverPort)
	return p.conn.UpdatePortProxyOnlineStatus(p.clientId, p.clientPort, OFFLINE)
}

// 转发消息至发起请求的客户端
func (p *PortProxyTunnel) StartTransData2RequestClient(conn net.Conn) {
	defer conn.Close()

	// 包装连接以自动监控TCP流量
	trafficMonitor := monitor.GetGlobalMonitor()
	monitoredConn := trafficMonitor.NewMonitoredConn(conn, p.clientId, p.serverPort)
	defer monitoredConn.Close()

	tunnel := p.conn.GetTunnelByServerPort(p.serverPort)
	if tunnel == nil {
		log.Printf("Failed to get tunnel for server port: %d", p.serverPort)
		return
	}

	// 获取smux会话
	session := tunnel.GetSession()
	if session == nil {
		log.Printf("Failed to get smux session")
		return
	}

	// 1. 新建一个stream
	stream, err := session.OpenStream()
	if err != nil {
		log.Printf("Failed to open stream: %v", err)
		return
	}
	defer stream.Close()

	// 2. 构建relay.Request协议头并发送
	targetAddr := p.clientPort // 端口代理使用clientPort作为目标地址
	request := relay.NewRequestMessage(relay.ProtoTCP, targetAddr)
	requestBytes, err := request.Encode()
	if err != nil {
		log.Printf("Failed to encode relay request: %v", err)
		return
	}

	// 发送协议头
	if _, err := stream.Write(requestBytes); err != nil {
		log.Printf("Failed to send relay request: %v", err)
		return
	}

	// 3. 等待客户端的确认响应
	confirmMsg, err := relay.DecodeConfirmMessage(stream)
	if err != nil {
		log.Printf("Failed to decode confirm message: %v", err)
		return
	}

	if !confirmMsg.Success {
		log.Printf("Client connection failed: %s", confirmMsg.Message)
		return
	}

	// 4. 开始双向数据转发
	go func() {
		io.Copy(stream, monitoredConn)
	}()

	_, err = io.Copy(monitoredConn, stream)
	if err != nil {
		log.Printf("Failed to copy data from stream: %v", err)
	}
}

// URL代理
type URLProxyTunnel struct {
	clientId string
	tunnel   *entity.SafeConn // 用来转发消息的真实通道
	conn     *Connection      // 系统内conn对象
}

func NewURLProxyTunnel(conn *Connection, clientUUID string) *URLProxyTunnel {
	return &URLProxyTunnel{
		clientId: clientUUID,
		tunnel:   conn.GetTunnel(clientUUID),
		conn:     conn,
	}
}

func (u *URLProxyTunnel) GetSafeConn() *entity.SafeConn {
	return u.tunnel
}
