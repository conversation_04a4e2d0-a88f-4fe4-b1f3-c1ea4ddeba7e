package entity

import (
	"fmt"
	"time"
)

// TunnelMonitor 隧道监控实体
type TunnelMonitor struct {
	ID          int             `json:"id"`
	ServerRoute string          `json:"server_route"` // 服务端代理路由（HTTP类型使用）
	ServerPort  int             `json:"server_port"`  // 服务端代理端口（TCP类型使用）
	TrafficData TrafficDataList `json:"traffic_data"` // 时序流量数据
	ClientGroup string          `json:"client_group"` // 客户端所在区域
	TunnelType  TunnelType      `json:"tunnel_type"`  // 隧道类型
	ClientUUID  string          `json:"client_uuid"`  // 客户端UUID
	ClientIP    string          `json:"client_ip"`    // 客户端IP
	ClientName  string          `json:"client_name"`  // 客户端名称
	ServiceName string          `json:"service_name"` // 服务名称
	CreateTime  time.Time       `json:"create_time"`  // 创建时间
	UpdateTime  time.Time       `json:"update_time"`  // 更新时间
}

// NewHTTPMonitor 创建HTTP监控实体
func NewHTTPMonitor(client *ClientInfo, serverRoute, serviceName string) *TunnelMonitor {
	now := time.Now()
	return &TunnelMonitor{
		ServerRoute: serverRoute,
		ServerPort:  0,
		TrafficData: TrafficDataList{},
		ClientGroup: client.Group,
		TunnelType:  TunnelTypeHTTP,
		ClientUUID:  client.UUID,
		ClientIP:    client.IP,
		ClientName:  client.Name,
		ServiceName: serviceName,
		CreateTime:  now,
		UpdateTime:  now,
	}
}

// NewTCPMonitor 创建TCP监控实体
func NewTCPMonitor(client *ClientInfo, serverPort int, serviceName string) *TunnelMonitor {
	now := time.Now()
	return &TunnelMonitor{
		ServerRoute: "",
		ServerPort:  serverPort,
		TrafficData: TrafficDataList{},
		ClientGroup: client.Group,
		TunnelType:  TunnelTypeTCP,
		ClientUUID:  client.UUID,
		ClientIP:    client.IP,
		ClientName:  client.Name,
		ServiceName: serviceName,
		CreateTime:  now,
		UpdateTime:  now,
	}
}

// AddTrafficData 添加流量数据
func (m *TunnelMonitor) AddTrafficData(data *TrafficData) {
	m.TrafficData.Add(data)
	m.UpdateTime = time.Now()
}

// GetTrafficDataInRange 获取指定时间范围内的流量数据
func (m *TunnelMonitor) GetTrafficDataInRange(start, end time.Time) TrafficDataList {
	return m.TrafficData.GetInRange(start, end)
}

// GetTotalTraffic 获取总流量统计
func (m *TunnelMonitor) GetTotalTraffic() (totalRequests int64, totalBytesIn int64, totalBytesOut int64) {
	return m.TrafficData.GetTotalTraffic()
}

// IsHTTPMonitor 判断是否为HTTP监控
func (m *TunnelMonitor) IsHTTPMonitor() bool {
	return m.TunnelType == TunnelTypeHTTP
}

// IsTCPMonitor 判断是否为TCP监控
func (m *TunnelMonitor) IsTCPMonitor() bool {
	return m.TunnelType == TunnelTypeTCP
}

// GetIdentifier 获取监控标识符
func (m *TunnelMonitor) GetIdentifier() string {
	if m.IsHTTPMonitor() {
		return m.ClientUUID + ":" + m.ServerRoute
	}
	return m.ClientUUID + ":" + string(rune(m.ServerPort))
}

// TrafficSummary 流量汇总信息
type TrafficSummary struct {
	ClientUUID   string    `json:"client_uuid"`
	TimeRange    string    `json:"time_range"`
	StartTime    time.Time `json:"start_time"`
	EndTime      time.Time `json:"end_time"`
	HTTPRequests int64     `json:"http_requests"`
	TCPBytesIn   int64     `json:"tcp_bytes_in"`
	TCPBytesOut  int64     `json:"tcp_bytes_out"`
	MonitorCount int       `json:"monitor_count"`
}

// NewTrafficSummary 创建流量汇总
func NewTrafficSummary(clientUUID string, hours int) *TrafficSummary {
	end := time.Now()
	start := end.Add(-time.Duration(hours) * time.Hour)

	return &TrafficSummary{
		ClientUUID:   clientUUID,
		TimeRange:    fmt.Sprintf("Last %d hours", hours),
		StartTime:    start,
		EndTime:      end,
		HTTPRequests: 0,
		TCPBytesIn:   0,
		TCPBytesOut:  0,
		MonitorCount: 0,
	}
}

// AddMonitorData 添加监控数据到汇总
func (s *TrafficSummary) AddMonitorData(monitor *TunnelMonitor) {
	s.MonitorCount++
	totalRequests, totalBytesIn, totalBytesOut := monitor.GetTotalTraffic()
	s.HTTPRequests += totalRequests
	s.TCPBytesIn += totalBytesIn
	s.TCPBytesOut += totalBytesOut
}
