#!/bin/bash

# 隧道系统测试运行脚本 - 简化版
# Linux/Unix 环境下的测试脚本

echo "=== Tunnel System Integration Tests ==="
echo

# 检查当前目录
if [ ! -f "tunnel_test.go" ]; then
    echo "Error: Please run this script in the test directory"
    exit 1
fi

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${YELLOW}--- Running Basic Integration Tests ---${NC}"
echo "Description: Test config loading, database connection, client registration"
echo "Command: go test -v -run TestTunnelIntegration"
echo

if go test -v -run TestTunnelIntegration; then
    echo -e "${GREEN}SUCCESS: Basic integration tests passed${NC}"
else
    echo -e "${RED}FAILED: Basic integration tests failed${NC}"
fi
echo

echo -e "${YELLOW}--- Running Client-Server Integration Tests ---${NC}"
echo "Description: Test client and server config file integration"
echo "Command: go test -v -run TestClientServerIntegration"
echo

if go test -v -run TestClientServerIntegration; then
    echo -e "${GREEN}SUCCESS: Client-Server integration tests passed${NC}"
else
    echo -e "${RED}FAILED: Client-Server integration tests failed${NC}"
fi
echo

echo -e "${YELLOW}--- Running All Basic Tests ---${NC}"
echo "Description: Run all tests that don't require CGO"
echo "Command: go test -v"
echo

if go test -v; then
    echo -e "${GREEN}SUCCESS: All basic tests passed${NC}"
else
    echo -e "${RED}FAILED: Some basic tests failed${NC}"
fi
echo

echo -e "${YELLOW}--- Checking CGO and SQLite3 Support ---${NC}"
echo "Checking CGO support..."

# 检查 GCC
if command -v gcc >/dev/null 2>&1; then
    echo -e "${GREEN}SUCCESS: GCC compiler available${NC}"
    
    # 检查 SQLite3 开发库
    if pkg-config --exists sqlite3 2>/dev/null || [ -f /usr/include/sqlite3.h ]; then
        echo -e "${GREEN}SUCCESS: SQLite3 development libraries available${NC}"
        echo "Running SQLite3 tests..."
        
        if CGO_ENABLED=1 go test -v -run TestTunnelIntegrationWithSQLite -tags cgo; then
            echo -e "${GREEN}SUCCESS: SQLite3 integration tests passed${NC}"
        else
            echo -e "${RED}FAILED: SQLite3 integration tests failed${NC}"
        fi
    else
        echo -e "${YELLOW}WARNING: SQLite3 development libraries not found${NC}"
        echo "To install SQLite3 development libraries:"
        echo "  Ubuntu/Debian: sudo apt-get install libsqlite3-dev"
        echo "  CentOS/RHEL:   sudo yum install sqlite-devel"
        echo "  Alpine:        sudo apk add sqlite-dev"
    fi
else
    echo -e "${YELLOW}WARNING: GCC compiler not found${NC}"
    echo "To install GCC:"
    echo "  Ubuntu/Debian: sudo apt-get install build-essential"
    echo "  CentOS/RHEL:   sudo yum groupinstall 'Development Tools'"
    echo "  Alpine:        sudo apk add build-base"
fi

echo
echo -e "${GREEN}=== Tests Completed ===${NC}"
echo
echo "Test Notes:"
echo "- Basic tests use in-memory database, no external dependencies required"
echo "- SQLite3 tests require CGO support and GCC compiler"
echo "- All tests use different port ranges to avoid conflicts"
echo
echo "For detailed instructions, please refer to README.md file"

# 显示系统信息
echo
echo -e "${YELLOW}=== System Information ===${NC}"
echo "OS: $(uname -s)"
echo "Architecture: $(uname -m)"
echo "Go Version: $(go version 2>/dev/null || echo 'Not installed')"
if command -v gcc >/dev/null 2>&1; then
    echo "GCC Version: $(gcc --version | head -n1)"
else
    echo "GCC: Not installed"
fi
