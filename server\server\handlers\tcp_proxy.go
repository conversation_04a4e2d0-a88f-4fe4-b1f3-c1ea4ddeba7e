package handlers

import (
	"bufio"
	"fmt"
	"io"
	"log"
	"net"
	"net/http"
	"net/http/httputil"
	"strings"
	"sync"

	"socks/relay"
	"socks/server/application/service"
	"socks/server/domain/entity"
	"socks/server/monitor"
	"socks/server/util"
)

var (
	tcpProxyHandler *TCPProxyHandler
	tphOnce         sync.Once
)

type TCPProxyHandler struct {
	urlProxyService service.UrlProxyService
}

func GetTCPProxyHandler() *TCPProxyHandler {
	tphOnce.Do(func() {
		tcpProxyHandler = &TCPProxyHandler{
			urlProxyService: service.GetUrlProxyService(util.SystemConfig),
		}
	})
	return tcpProxyHandler
}

// StartTCPProxyServer 启动TCP代理服务器
func (h *TCPProxyHandler) StartTCPProxyServer(port int) error {
	listener, err := net.Listen("tcp", fmt.Sprintf("0.0.0.0:%d", port))
	if err != nil {
		return fmt.Errorf("failed to start TCP proxy server on port %d: %v", port, err)
	}

	go func() {
		defer listener.Close()
		for {
			conn, err := listener.Accept()
			if err != nil {
				log.Printf("TCP proxy server accept connection fail: %v", err)
				continue
			}
			log.Printf("New connection accepted, ip: %s", conn.RemoteAddr())
			// 为每个连接启动处理协程
			go h.handleTCPConnection(conn.(*net.TCPConn))
		}
	}()

	return nil
}

// handleTCPConnection 处理TCP连接，支持同一连接发送多次HTTP请求
func (h *TCPProxyHandler) handleTCPConnection(clientConn *net.TCPConn) {
	defer func() {
		clientConn.Close()
	}()

	reader := bufio.NewReader(clientConn)

	// 循环处理多个HTTP请求，直到连接关闭
	for {
		// 1. 从 TCP 流中读取并解析完整的 HTTP 请求
		// ReadRequest 会处理好请求头、请求体的边界问题（如 Content-Length 或分块编码）
		req, err := http.ReadRequest(reader)
		if err != nil {
			// 如果连接在读取任何数据前就关闭了，这是正常情况，无需报错
			if err == io.EOF {
				log.Printf("Client connection closed normally: %s", clientConn.RemoteAddr())
			} else {
				log.Printf("Failed to read/parse HTTP request for conn %s", clientConn.RemoteAddr())
			}
			return
		}

		// 处理单个HTTP请求
		if !h.handleSingleRequest(clientConn, req) {
			// 如果处理失败或需要关闭连接，退出循环
			return
		}

		// 检查是否需要保持连接
		// HTTP/1.0 默认关闭连接，HTTP/1.1 默认保持连接
		if req.ProtoMajor == 1 && req.ProtoMinor == 0 {
			// HTTP/1.0 - 检查是否有 Connection: keep-alive
			if strings.ToLower(req.Header.Get("Connection")) != "keep-alive" {
				return
			}
		} else {
			// HTTP/1.1 - 检查是否有 Connection: close
			if strings.ToLower(req.Header.Get("Connection")) == "close" {
				return
			}
		}
	}
}

// handleSingleRequest 处理单个HTTP请求，返回是否应该继续处理后续请求
func (h *TCPProxyHandler) handleSingleRequest(clientConn net.Conn, req *http.Request) bool {
	// 查找映射并构建目标路径
	mapping, baseURL, targetPath, ipPort, err := h.lookupMappingAndBuildPath(req.RequestURI)
	if err != nil {
		log.Printf("Failed to lookup mapping for %s: %v", req.RequestURI, err)
		h.sendErrorResponse(clientConn, 404, "Not Found")
		return false
	}

	// 检查映射是否在线
	if !mapping.Online {
		log.Printf("Mapping for %s is offline", req.RequestURI)
		h.sendErrorResponse(clientConn, 503, "Service Unavailable")
		return false
	}

	// 获取客户端的SafeConn连接
	tunnel := h.urlProxyService.GetTunnel(mapping.Client.UUID)
	if tunnel == nil {
		log.Printf("Tunnel for %s is not available", mapping.Client.UUID)
		h.sendErrorResponse(clientConn, 503, "Service Unavailable")
		return false
	}

	// 2. 修改请求的路由和 Host 属性
	// 直接修改解析后的请求对象的字段，比字符串替换更安全、更精确
	req.RequestURI = targetPath
	req.Host = ipPort
	// 清理 URL 字段，让 DumpRequest 基于 RequestURI 和 Host 重建请求行
	req.URL.Path = targetPath
	req.URL.Scheme = ""
	req.URL.Host = ""
	req.Header.Set("origin", "http://"+ipPort)
	// 3. 将修改后的请求对象序列化回字节流
	// 第二个参数 'true' 表示也包含请求体
	modifiedReqBytes, err := httputil.DumpRequest(req, true)
	if err != nil {
		log.Printf("Failed to dump modified request for conn %s", clientConn.RemoteAddr())
		return false
	}

	// 记录HTTP请求到流量监控系统
	trafficMonitor := monitor.GetGlobalMonitor()
	trafficMonitor.RecordHTTPRequest(mapping.Client.UUID, baseURL)

	// 处理TCP连接转发
	return h.handleSingleTCPForward(clientConn, modifiedReqBytes, tunnel, ipPort)
}

// handleSingleTCPForward 处理单个TCP请求转发，返回是否应该继续处理后续请求
func (h *TCPProxyHandler) handleSingleTCPForward(clientConn net.Conn, reqBytes []byte, tunnel *entity.SafeConn, ipPort string) bool {
	// 获取smux会话
	session := tunnel.GetSession()
	if session == nil {
		log.Printf("Failed to get smux session")
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return false
	}

	// 1. 新建一个stream
	stream, err := session.OpenStream()
	if err != nil {
		log.Printf("Failed to open stream: %v", err)
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return false
	}

	// 2. 构建relay.Request协议头并发送 - 使用HTTP协议类型
	request := relay.NewRequestMessage(relay.ProtoHTTP, ipPort)
	requestBytes, err := request.Encode()
	if err != nil {
		log.Printf("Failed to encode relay request")
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return false
	}

	// 发送协议头
	if _, err := stream.Write(requestBytes); err != nil {
		log.Printf("Failed to send relay request")
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return false
	}

	// 3. 等待客户端的确认响应
	confirmMsg, err := relay.DecodeConfirmMessage(stream)
	if err != nil {
		log.Printf("Failed to decode confirm message")
		h.sendErrorResponse(clientConn, 500, "Internal Server Error")
		return false
	}

	if !confirmMsg.Success || confirmMsg.Type != relay.MessageTypeConfirm {
		log.Printf("Client connection failed: %s, %d", confirmMsg.Message, confirmMsg.Type)
		h.sendErrorResponse(clientConn, 503, "Service Unavailable")
		return false
	}

	// 4. 开始数据传输 - 发送请求数据
	if _, err := stream.Write(reqBytes); err != nil {
		log.Printf("Failed to write request data to stream")
		return false
	}

	// 5. 启动goroutine将客户端响应返回至用户连接
	go func() {
		defer stream.Close()
		io.Copy(clientConn, stream)
	}()

	return true
}

// sendErrorResponse 发送错误响应
func (h *TCPProxyHandler) sendErrorResponse(conn net.Conn, statusCode int, statusText string) {
	response := fmt.Sprintf("HTTP/1.1 %d %s\r\nContent-Type: text/plain\r\nContent-Length: %d\r\n\r\n%s",
		statusCode, statusText, len(statusText), statusText)
	conn.Write([]byte(response))
}

// lookupMappingAndBuildPath 查找映射并构建目标路径（复用URLProxyHandler的逻辑）
func (h *TCPProxyHandler) lookupMappingAndBuildPath(urlPath string) (*entity.URLMapping, string, string, string, error) {
	urlParts := strings.Split(strings.Trim(urlPath, "/"), "/")
	if len(urlParts) < 4 {
		return nil, "", "", "0", fmt.Errorf("invalid URL path")
	}

	var mapping *entity.URLMapping
	var targetPath string

	// 根据客户端类型查找匹配的映射
	switch strings.ToUpper(urlParts[1]) {
	case "AGENT":
		if len(urlParts) >= 4 {
			// Agent路径格式: /Tunnel/Agent/客户端名称/服务名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 4 {
				targetPath = "/" + strings.Join(urlParts[4:], "/")
			} else {
				targetPath = "/"
			}
		}
	case "AI", "API", "ALG":
		if len(urlParts) >= 5 {
			// AI/API/ALG路径格式: /(AI|API)/服务组别/服务名称/客户端名称/...
			urlPath := fmt.Sprintf("/%s/%s/%s/%s/%s", urlParts[0], urlParts[1], urlParts[2], urlParts[3], urlParts[4])
			mapping = h.urlProxyService.GetURLMapping(urlPath)
			if len(urlParts) > 5 {
				targetPath = "/" + strings.Join(urlParts[5:], "/")
			} else {
				targetPath = "/"
			}
		}
	default:
		return nil, "", "", "", fmt.Errorf("unsupported API type: %s", urlParts[1])
	}

	if mapping == nil {
		return nil, "", "", "", fmt.Errorf("no mapping found for path: %s", urlPath)
	}

	// 获取最长匹配的baseURL
	baseUrl, ipPort := mapping.MatchBaseURL(targetPath)

	return mapping, baseUrl, targetPath, ipPort, nil
}
