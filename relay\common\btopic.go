package common

const (
	// 一张图
	OneMap = "onemap"
	// 开发测试
	Dev = "dev"
	// 时空计算平台
	STCP = "stcp"
)

var (
	topic = map[string]string{
		OneMap: "一张图",
		Dev:    "开发测试",
		STCP:   "时空计算平台",
	}
)

func CreateNewTopic(en, zh string) string {
	topic[en] = zh
	return en
}

func GetTopicZhName(en string) string {
	return topic[en]
}

func GetAllTopicKey() []string {
	keys := make([]string, 0, len(topic))
	for k := range topic {
		keys = append(keys, k)
	}
	return keys
}

func GetAllTopicValue() []string {
	values := make([]string, 0, len(topic))
	for _, v := range topic {
		values = append(values, v)
	}
	return values
}

func IsTopicExist(en string) bool {
	_, ok := topic[en]
	return ok
}

func GetDefaultTopic() string {
	return Dev
}
