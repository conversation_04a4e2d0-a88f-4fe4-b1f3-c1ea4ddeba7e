package event

import (
	"fmt"
	"log"
	"net"
	"sync"
	"time"

	"socks/server/domain/entity"
	"socks/server/domain/repo"
	"socks/server/monitor"
	"socks/server/util"
)

var (
	once              sync.Once
	connection        *Connection
	intranetTunnelDao repo.PortMappingDao
	// URL代理响应处理函数，由handlers包设置
	URLProxyResponseHandler func(*entity.URLProxyMessage)
)

type Connection struct {
	listeners    *entity.ListenerGroup
	tunnels      *entity.TunnelGroup
	portMappings *entity.PortMappingGroup
	urlMappings  *entity.URLMappingGroup
	clients      *RegisterClient
}

func GetConnection(config *util.TunnelConfig) *Connection {
	once.Do(func() {
		connection = &Connection{
			listeners:    entity.GetListenerGroup(),
			tunnels:      entity.GetTunnelGroup(),
			portMappings: entity.GetPortMappingGroup(config),
			urlMappings:  entity.GetURLMappingGroup(),
			clients:      GetRegisterClient(), // TODO: 放到entity里面
		}
		intranetTunnelDao = repo.GetPortMappingDao()
	})
	return connection
}

func (c *Connection) AddMapping(uuid string, ipPort string, mapping *entity.PortMapping) {
	c.portMappings.AddMapping(uuid, ipPort, mapping)

	// 持久化
	id, err := intranetTunnelDao.Create(repo.PortMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("save port mapping 2 pg fail, err: %v", err)
	}
	// 更新mapping的id，用以后续更新通道状态
	mapping.ID = id
}

func (c *Connection) GetMapping(uuid, ipPort string) *entity.PortMapping {
	return c.portMappings.GetMapping(uuid, ipPort)
}

func (c *Connection) DeleteMapping(uuid string, ipPort string) {
	id := c.portMappings.DeleteMapping(uuid, ipPort)
	if id == entity.EMPTY_MAPPING_ID {
		return
	}

	err := intranetTunnelDao.Delete(id)
	if err != nil {
		log.Printf("delete port mapping from pg fail, err: %v", err)
	}
}

func (c *Connection) UpdatePortProxyOnlineStatus(uuid string, ipPort string, online bool) error {
	// 更新缓存对象
	c.portMappings.UpdateOnlineStatus(uuid, ipPort, online)
	// 更新数据库对象
	mapping := c.portMappings.GetMapping(uuid, ipPort)
	return intranetTunnelDao.Update(repo.PortMapping2IntranetTunnel(mapping))
}

func (c *Connection) UpdateClientPortOnlineStatus(clientUUID string, online bool) {
	c.portMappings.Locker.Lock()
	defer c.portMappings.Locker.Unlock()
	for _, mapping := range c.portMappings.Mappings {
		if mapping.Client.UUID == clientUUID {
			mapping.Online = online
			intranetTunnelDao.Update(repo.PortMapping2IntranetTunnel(mapping))
		}
	}
}

// UpdateLastConnectionTime 更新最后连接时间
func (c *Connection) UpdateLastConnectionTime(clientUUID, ipPort string) error {
	// 更新缓存对象
	c.portMappings.UpdateLastConnectionTime(clientUUID, ipPort)
	// 更新数据库对象
	mapping := c.portMappings.GetMapping(clientUUID, ipPort)
	if mapping != nil {
		return intranetTunnelDao.UpdateLastConnectionTime(mapping.ID)
	}
	return nil
}

// CleanExpiredMappings 清理过期的端口映射
func (c *Connection) CleanExpiredMappings(expiration time.Duration) error {
	// 获取过期的映射
	expiredMappings := c.portMappings.GetExpiredMappings(expiration)
	if len(expiredMappings) == 0 {
		return nil
	}

	log.Printf("find %d expired port mappings, ready to clean", len(expiredMappings))

	// 收集需要删除的数据库记录ID
	var idsToDelete []int
	for _, mapping := range expiredMappings {
		if mapping.ID > 0 {
			idsToDelete = append(idsToDelete, mapping.ID)
		}

		// 从缓存中删除映射
		c.portMappings.DeleteMapping(mapping.Client.UUID, mapping.ClientIpPort)

		// 关闭监听器
		if mapping.Listener != nil {
			c.listeners.DeleteListener(mapping.ServerPort)
		}

		log.Printf("clean expired port mapping: client=%s, client port=%s, server port=%d, last connection time=%v",
			mapping.Client.UUID, mapping.ClientIpPort, mapping.ServerPort, mapping.Connected)
	}

	// 批量删除数据库记录
	if len(idsToDelete) > 0 {
		err := intranetTunnelDao.BatchDelete(idsToDelete)
		if err != nil {
			return err
		}
	}

	return nil
}

func (c *Connection) IsServerPortAllocated(serverPort int) bool {
	return c.portMappings.GetClientUUIDbyServerPort(serverPort) != ""
}

// URL映射管理方法
func (c *Connection) AddURLMapping(mapping *entity.URLMapping) {
	c.urlMappings.AddMapping(mapping)
	// 持久化
	id, err := intranetTunnelDao.Create(repo.URLMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("save url mapping 2 pg fail, err: %v", err)
	}
	// 更新mapping的id，用以后续更新通道状态
	mapping.ID = id
}

func (c *Connection) UpdateUrlMapping(mapping *entity.URLMapping) {
	c.urlMappings.UpdateMapping(mapping)
	// 持久化
	err := intranetTunnelDao.Update(repo.URLMapping2IntranetTunnel(mapping))
	if err != nil {
		log.Printf("update url mapping 2 pg fail, err: %v", err)
	}
}

func (c *Connection) GetURLMapping(urlPath string) *entity.URLMapping {
	return c.urlMappings.GetMapping(urlPath)
}

func (c *Connection) DeleteURLMappingByUrlPath(urlPath string) {
	mapping := c.urlMappings.DeleteMapping(urlPath)
	err := intranetTunnelDao.Delete(mapping.ID)
	if err != nil {
		log.Printf("delete url mapping from pg fail, err: %v", err)
	}
}

func (c *Connection) DeleteURLMappingByBaseUrl(urlPath, baseUrl string) {
	mapping, needDelete := c.urlMappings.DeleteBaseUrlFromMapping(urlPath, baseUrl)
	if needDelete {
		err := intranetTunnelDao.Delete(mapping.ID)
		if err != nil {
			log.Printf("delete url mapping from pg fail, err: %v", err)
		}
	} else {
		// 更新数据库对象
		err := intranetTunnelDao.Update(repo.URLMapping2IntranetTunnel(mapping))
		if err != nil {
			log.Printf("update url mapping 2 pg fail, err: %v", err)
		}
	}
}

func (c *Connection) GetClientURLMappings(clientUUID string) []string {
	return c.urlMappings.GetClientPaths(clientUUID)
}

func (c *Connection) UpdateClientUrlOnlineStatus(clientUUID string, online bool) {
	c.urlMappings.Locker.Lock()
	defer c.urlMappings.Locker.Unlock()
	for _, mapping := range c.urlMappings.Mappings {
		if mapping.Client.UUID == clientUUID {
			mapping.Online = online
			intranetTunnelDao.Update(repo.URLMapping2IntranetTunnel(mapping))
		}
	}
}

// 客户端管理方法
func (c *Connection) RegisterClient(client *entity.Client) error {
	return c.clients.AddClient(client)
}

func (c *Connection) GetClient(clientUUID string) *entity.Client {
	return c.clients.GetClient(clientUUID)
}

func (c *Connection) UnregisterClient(clientUUID string) {
	c.clients.DeleteClient(clientUUID)

	// 清理客户端的所有流量监控记录
	trafficMonitor := monitor.GetGlobalMonitor()
	err := trafficMonitor.DeleteMonitorsByClient(clientUUID)
	if err != nil {
		log.Printf("Failed to cleanup traffic monitors for client %s: %v", clientUUID, err)
	}
}

func (c *Connection) GetAllClients() map[string]*entity.Client {
	return c.clients.GetAllClients()
}

func (c *Connection) FilterClients(filter ClientFilter) []*entity.Client {
	return c.clients.FilterClients(filter)
}

func (c *Connection) GetClientsByGroup() map[string][]*entity.Client {
	return c.clients.GetClientsByGroup()
}

func (c *Connection) RecoverFromDB() error {
	// 从数据库获取所有隧道记录
	tunnels, err := intranetTunnelDao.GetAll()
	if err != nil {
		return fmt.Errorf("get all interner tunnels error: %v", err)
	}

	// 遍历所有记录并恢复映射
	for _, tunnel := range tunnels {
		// 将数据库记录转换为 PortMapping 对象
		if tunnel.ServerPort == 0 {
			log.Printf("recover from db, url path: %s, client: %s", tunnel.ClientRoute, tunnel.ClientUUID)
			c.updateUrlMappingFromRecord(repo.IntranetTunnel2URLMapping(tunnel))
		} else {
			log.Printf("recover from db, server port: %d, client port: %s, client: %s", tunnel.ServerPort, tunnel.ClientPort, tunnel.ClientUUID)
			c.updateProtMappingFromRecord(repo.IntranetTunnel2PortMapping(tunnel))
		}

	}
	return nil
}

func (c *Connection) UpdateFromDB(ids []int, all bool) error {
	// 根据是否传入ids决定获取方式
	tunnels, err := intranetTunnelDao.GetIntranetTunnelsByIDs(ids)
	if err != nil {
		return fmt.Errorf("get interner tunnels by ids error: %v", err)
	}
	if all {
		tunnels, err = intranetTunnelDao.GetAll()
		if err != nil {
			return fmt.Errorf("get all interner tunnels error: %v", err)
		}
	}

	// 创建记录更新的映射集合
	updatedPortMappings := make(map[string]bool) // key: "clientUUID:clientIpPort"
	updatedURLMappings := make(map[string]bool)  // key: "urlPath"

	// 更新现有映射
	for _, tunnel := range tunnels {
		if tunnel.ServerPort == 0 {
			urlMapping := repo.IntranetTunnel2URLMapping(tunnel)
			c.updateUrlMappingFromRecord(urlMapping)
			// 记录所有被更新的URL映射
			updatedURLMappings[urlMapping.URLPath] = true
		} else {
			portMapping := repo.IntranetTunnel2PortMapping(tunnel)
			c.updateProtMappingFromRecord(portMapping)
			// 记录被更新的端口映射
			key := fmt.Sprintf("%s:%s", portMapping.Client.UUID, portMapping.ClientIpPort)
			updatedPortMappings[key] = true
		}
	}

	log.Printf("start clean unupdated mappings")

	// 清理未更新的映射
	c.cleanUnupdatedMappings(updatedPortMappings, updatedURLMappings)

	log.Printf("update from db success, updated %d port mappings and %d url mappings",
		len(updatedPortMappings), len(updatedURLMappings))
	return nil
}

// cleanUnupdatedMappings 清理未被更新的映射
func (c *Connection) cleanUnupdatedMappings(updatedPortMappings, updatedURLMappings map[string]bool) {
	c.portMappings.CleanUnupdatedMappings(updatedPortMappings)
	c.urlMappings.CleanUnupdatedMappings(updatedURLMappings)
}

func (c *Connection) AddTunnel(uuid string, conn net.Conn) {
	c.tunnels.AddTunnel(uuid, entity.NewSafeConn(conn))
}

func (c *Connection) GetTunnel(uuid string) *entity.SafeConn {
	return c.tunnels.GetTunnel(uuid)
}

func (c *Connection) GetTunnelByServerPort(serverPort int) *entity.SafeConn {
	cilentUUID := c.portMappings.GetClientUUIDbyServerPort(serverPort)
	if cilentUUID == "" {
		log.Printf("not find server-client conn")
		return nil
	}
	return c.GetTunnel(cilentUUID)
}

func (c *Connection) ActivateTunnel(uuid string) {
	tunnel := c.tunnels.GetTunnel(uuid)
	if tunnel == nil {
		log.Printf("tunnel is nil, client uuid: %s", uuid)
		return
	}

	// 启动smux.Stream处理（新的处理方式）
	go c.handleSmuxStreams(uuid, tunnel)
}

// handleSmuxStreams 处理基于smux的stream连接（新增方法）
func (c *Connection) handleSmuxStreams(uuid string, tunnel *entity.SafeConn) {
	session := tunnel.GetSession()
	if session == nil {
		log.Printf("No smux session available for client: %s", uuid)
		return
	}

	// 这个方法主要用于服务端接受来自客户端的stream连接
	// 在当前的设计中，服务端是主动创建stream的一方
	// 所以这里主要是为了保持会话活跃和处理可能的客户端主动连接

	// 当前只用来监控客户端连接的状态，同时更新uuid客户端对应的代理记录online状态
	log.Printf("Smux stream handler started for client: %s", uuid)

	for {
		// 循环接收新的stream
		_, err := session.AcceptStream()
		if err != nil {
			log.Printf("Failed to accept stream for client %s: %v", uuid, err)
			c.ClientOffline(uuid)
			break
		}
	}
}

func (c *Connection) CloseTunnel(uuid string) {
	c.tunnels.CloseTunnel(uuid)
	c.portMappings.UpdateOnlineStatusByClientStatus(uuid, false)
}

func (c *Connection) AllocatePort() (int, error) {
	listener := &entity.Listener{}
	for port := c.portMappings.MinPort; port <= c.portMappings.MaxPort; port++ {
		if c.IsServerPortAllocated(port) {
			continue
		}
		err := listener.BuildListener("tcp", "0.0.0.0", port)
		if err != nil {
			continue
		}
		c.listeners.AddListener(port, listener)
		return port, nil
	}

	return 0, fmt.Errorf("no available port")
}

func (c *Connection) GetListener(serverPort int) *entity.Listener {
	return c.listeners.GetListener(serverPort)
}

func (c *Connection) CloseListener(serverPort int) {
	c.listeners.DeleteListener(serverPort)
}

func (c *Connection) ClientOffline(uuid string) {
	// 1. 更新两个mapping中的状态
	// 2. 删除register client中的client
	// 4. safeConn 需要关闭
	c.clients.Off(uuid)
	c.tunnels.CloseTunnel(uuid)
	c.UpdateClientPortOnlineStatus(uuid, false)
	c.UpdateClientUrlOnlineStatus(uuid, false)
}

func (c *Connection) updateProtMappingFromRecord(mapping *entity.PortMapping) {
	existingMapping := c.portMappings.GetMapping(mapping.Client.UUID, mapping.ClientIpPort)
	if existingMapping != nil {
		mapping.Listener = existingMapping.Listener
		c.portMappings.AddMapping(mapping.Client.UUID, mapping.ClientIpPort, mapping)
	} else {
		listener := &entity.Listener{}
		err := listener.BuildListener("tcp", "0.0.0.0", mapping.ServerPort)
		if err != nil {
			log.Printf("create listener failed: %v", err)
			return
		}
		mapping.Listener = listener
		c.listeners.AddListener(mapping.ServerPort, listener)
		c.portMappings.AddMapping(mapping.Client.UUID, mapping.ClientIpPort, mapping)
	}

	trafficMonitor := monitor.GetGlobalMonitor()
	clientInfo := monitor.ClientInfo{
		UUID:  mapping.Client.UUID,
		IP:    mapping.Client.IP,
		Name:  mapping.Client.Name,
		Group: mapping.Client.Group,
	}

	err := trafficMonitor.CreateTCPMonitor(clientInfo, mapping.ServerPort, mapping.ServiceName)
	if err != nil {
		// 记录错误但不影响端口分配
		log.Printf("Failed to create TCP monitor for port %d: %v", mapping.ServerPort, err)
	}

}

func (c *Connection) updateUrlMappingFromRecord(mapping *entity.URLMapping) {

	c.urlMappings.AddMapping(mapping)
	trafficMonitor := monitor.GetGlobalMonitor()
	clientInfo := monitor.ClientInfo{
		UUID:  mapping.Client.UUID,
		IP:    mapping.Client.IP,
		Name:  mapping.Client.Name,
		Group: mapping.Client.Group,
	}

	// 为每个BaseURL创建HTTP监控记录
	for baseURL, serviceInfo := range mapping.BaseURL {
		err := trafficMonitor.CreateHTTPMonitor(clientInfo, baseURL, serviceInfo.ServiceName)
		if err != nil {
			// 记录错误但不影响映射创建
			fmt.Printf("Failed to create HTTP monitor for %s: %v\n", baseURL, err)
		}
	}
}
