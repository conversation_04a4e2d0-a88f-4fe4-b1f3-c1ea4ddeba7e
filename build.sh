#!/bin/bash

set -e

CLIENT_PLATFORMS=(
  "windows amd64 .exe"
  "linux amd64"
)
SERVER_PLATFORMS=(
  "windows amd64 .exe"
)
build_module() {
  local dir=$1
  local name=$2
  echo "Building $name..."
  cd $dir
  go mod tidy
  for platform in "${PLATFORMS[@]}"; do
    set -- $platform
    GOOS=$1
    GOARCH=$2
    EXT=${3:-}
    OUT="../build/${name}${EXT}"
    echo "  $GOOS-$GOARCH -> $OUT"
    CGO_ENABLED=0 GOOS=$GOOS GOARCH=$GOARCH go build -o $OUT
  done
  cd ..
}

# build_module server TunnelGateway
build_client() {
  local dir=$1
  local name=$2
  echo "Building client $name..."
  cd $dir
  go mod tidy
  for platform in "${CLIENT_PLATFORMS[@]}"; do
    set -- $platform
    GOOS=$1
    GOARCH=$2
    EXT=${3:-}
    OUT="../build/${name}${EXT}"
    echo "  $GOOS-$GOARCH -> $OUT"
    CGO_ENABLED=0 GOOS=$GOOS GOARCH=$GOARCH go build -o $OUT
  done
  cd ..
}

build_server() {
  local dir=$1
  local name=$2
  echo "Building server $name..."
  cd $dir
  go mod tidy
  for platform in "${SERVER_PLATFORMS[@]}"; do
    set -- $platform
    GOOS=$1
    GOARCH=$2
    EXT=${3:-}
    OUT="../build/${name}${EXT}"
    echo "  $GOOS-$GOARCH -> $OUT"
    CGO_ENABLED=0 GOOS=$GOOS GOARCH=$GOARCH go build -o $OUT
  done
  cd ..
}
# build_module client BaseOS.Desktop.TunnelGateway

build_server server TunnelGateway
echo "Executables in build/:"
# 编译客户端（Windows 和 Linux）
build_client client BaseOS.Desktop.TunnelGateway
ls -1 build/