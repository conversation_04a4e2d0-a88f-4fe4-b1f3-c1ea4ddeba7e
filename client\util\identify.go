package util

import (
	"fmt"
	"log"
	"net"
	"os"
	"strconv"
	"strings"

	"github.com/google/uuid"
)

const (
	HEARTBEAT = "heartbeat"
)

func GetHostName() string {
	hostname, err := os.Hostname()
	if err != nil {
		log.Println("无法获取主机名:", err)
		return ""
	}

	return hostname
}

func GetLocalIP() (string, error) {
	// Prefer addresses from non-virtual, up interfaces.
	ifaces, err := net.Interfaces()
	if err != nil {
		return "127.0.0.1", err
	}

	// helper to detect virtual-like interface names
	isVirtualIf := func(name string) bool {
		// common virtual interface name prefixes
		virtualPrefixes := []string{"veth", "docker", "br-", "virbr", "vboxnet", "vmnet", "vnet", "vEthernet", "tun", "tap", "wg", "hyperv", "vmware"}
		lname := strings.ToLower(name)
		for _, p := range virtualPrefixes {
			if strings.HasPrefix(lname, strings.ToLower(p)) {
				return true
			}
		}
		return false
	}

	for _, iface := range ifaces {
		// skip down or loopback interfaces
		if iface.Flags&net.FlagUp == 0 {
			continue
		}
		if iface.Flags&net.FlagLoopback != 0 {
			continue
		}
		if isVirtualIf(iface.Name) {
			continue
		}

		addrs, err := iface.Addrs()
		if err != nil {
			continue
		}
		for _, addr := range addrs {
			if ipnet, ok := addr.(*net.IPNet); ok {
				ip := ipnet.IP
				if ip == nil || ip.IsLoopback() {
					continue
				}
				ip4 := ip.To4()
				if ip4 == nil {
					continue
				}
				s := ip4.String()
				// filter out undesired prefixes: loopback/link-local and virtual 172.* ranges
				if strings.HasPrefix(s, "127.") || strings.HasPrefix(s, "169.254.") {
					continue
				}
				// Skip **********/12 and other 172.* virtual ranges per request
				if strings.HasPrefix(s, "172.") {
					continue
				}
				// skip unspecified
				if s == "0.0.0.0" {
					continue
				}

				return s, nil
			}
		}
	}

	// Fallback: try any non-loopback IPv4 from InterfaceAddrs
	addrs, err := net.InterfaceAddrs()
	if err != nil {
		return "127.0.0.1", err
	}
	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipnet.IP.To4() != nil {
				return ipnet.IP.String(), nil
			}
		}
	}

	return "127.0.0.1", nil
}

func GenerateUUID() string {
	macAddr, err := getMACAddress()
	if err != nil {
		log.Println(err)
		return ""
	}

	ipAddr, err := GetLocalIP()
	if err != nil {
		log.Println(err)
		return ""
	}

	// 使用 MAC 地址和 IP 地址的哈希生成 UUID
	namespaceUUID := uuid.NewMD5(uuid.Nil, []byte(macAddr+ipAddr))

	return namespaceUUID.String()
}

func getMACAddress() (string, error) {
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, inter := range interfaces {
		if len(inter.HardwareAddr) > 0 {
			return inter.HardwareAddr.String(), nil
		}
	}
	return "", fmt.Errorf("no MAC address found")
}

func GetServerPortByMsgId(id string) (int, error) {
	if id == HEARTBEAT {
		return 0, nil
	}
	parts := strings.Split(id, "-")
	if len(parts) != 4 {
		return 0, fmt.Errorf("msg id illegal, id: %s", id)
	}
	serverPort, err := strconv.Atoi(parts[3])
	if err != nil {
		return 0, fmt.Errorf("msg id illegal, id: %s", id)
	}
	return serverPort, nil
}

func GetFormatServiceType(serviceType string) string {
	serviceTypeLow := strings.ToLower(serviceType)

	if strings.Contains(serviceTypeLow, "ai") {
		return "AI"
	} else if strings.Contains(serviceTypeLow, "api") {
		return "API"
	} else if strings.Contains(serviceTypeLow, "agent") {
		return "AGENT"
	} else if strings.Contains(serviceTypeLow, "alg") {
		return "ALG"
	} else {
		return ""
	}
}
