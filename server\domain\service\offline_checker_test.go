package service

import (
	"testing"

	"socks/server/domain/entity"
)

func TestClientOfflineChecker_PerformCheck(t *testing.T) {
	c1 := &entity.Client{UUID: "u1"}
	c2 := &entity.Client{UUID: "u2"}

	clients := map[string]*entity.Client{
		"u1": c1,
		"u2": c2,
	}

	checker := &ClientOfflineChecker{
		getAllClients: func() map[string]*entity.Client { return clients },
		getTunnel: func(uuid string) *entity.SafeConn {
			if uuid == "u1" {
				return &entity.SafeConn{} // online
			}
			return nil // offline
		},
	}

	checker.performCheck()

	counts1 := c1.GetOfflineDailyCounts()
	counts2 := c2.GetOfflineDailyCounts()
	if v := counts1[len(counts1)-1]; v != 0 {
		t.Fatalf("u1 latest day expected 0 offline, got %d", v)
	}
	if v := counts2[len(counts2)-1]; v != 1 {
		t.Fatalf("u2 latest day expected 1 offline, got %d", v)
	}
}
