package service

import (
	"socks/server/application/event"
	"socks/server/domain/entity"
	"socks/server/util"
)

type UrlProxyService interface {
	RegisterClient(client *entity.Client) error
	RegisterURLMapping(clientUUID, appName, baseURL, serviceName, serviceGroup, apiType, ipPort string) (string, error)
	UnregisterURLMapping(clientUUID, urlPath, baseURL string) error
	GetURLMapping(urlPath string) *entity.URLMapping
	GetClientURLMappings(clientUUID string) []string
	UpdateOnlineStatus(clientUUID string, online bool) error
	GetTunnel(clientUUID string) *entity.SafeConn
}

func GetUrlProxyService(config *util.TunnelConfig) UrlProxyService {
	return event.GetURLProxy(config)
}
