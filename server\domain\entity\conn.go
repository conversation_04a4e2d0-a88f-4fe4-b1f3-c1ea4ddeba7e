package entity

import (
	"log"
	"net"
	"sync"
	"time"

	"github.com/xtaci/smux"
)

var (
	tunnelGroup *TunnelGroup
	scgOnce     sync.Once
)

// Message 定义了 TCP/UDP 上传输的消息格式，用于多路复用
type ConnMessage struct {
	ID   string `json:"id"`             // 连接 ID
	Type string `json:"type"`           // open, data, close, proxy_request, proxy_response
	Data []byte `json:"data,omitempty"` // 二进制数据
}

// URLProxyMessage 定义了URL代理消息格式
type URLProxyMessage struct {
	ConnMessage
	BaseURL   string            `json:"base_url,omitempty"`   // 客户端映射的URL根路径，用来发送给客户端查找真实ip
	TargetURL string            `json:"target_url,omitempty"` // 目标URL，指定BaseURL下的目标url，包含BaseURL的部分
	Method    string            `json:"method,omitempty"`     // HTTP方法
	Headers   map[string]string `json:"headers,omitempty"`    // HTTP头
	Body      []byte            `json:"body,omitempty"`       // 请求/响应体
	Status    int               `json:"status,omitempty"`     // HTTP状态码（响应时使用）
	Error     string            `json:"error,omitempty"`      // 错误信息

	// 流式传输相关字段
	IsStream    bool   `json:"is_stream,omitempty"`     // 是否为流式传输
	StreamType  string `json:"stream_type,omitempty"`   // 流类型：stream_start, stream_chunk, stream_end
	ChunkIndex  int    `json:"chunk_index,omitempty"`   // 数据块索引
	IsLastChunk bool   `json:"is_last_chunk,omitempty"` // 是否为最后一个数据块
}

// SafeConn 包装 TCP 连接，保证并发写安全
type SafeConn struct {
	conn    net.Conn      // 底层 TCP 连接
	session *smux.Session // smux会话，用于多路复用
}

func NewSafeConn(conn net.Conn) *SafeConn {

	if tcpConn, ok := conn.(*net.TCPConn); ok {
		tcpConn.SetNoDelay(true)   // 禁用Nagle算法
		tcpConn.SetKeepAlive(true) // 启用TCP keepalive
		tcpConn.SetKeepAlivePeriod(30 * time.Second)
		tcpConn.SetWriteBuffer(64 * 1024) // 增大写缓冲区
		tcpConn.SetReadBuffer(64 * 1024)  // 增大读缓冲区
	}

	// 创建smux会话（服务端模式）
	// 配置smux参数，优化并发性能和保活机制
	config := smux.DefaultConfig()
	config.MaxReceiveBuffer = 4194304           // 4MB 接收缓冲区
	config.MaxStreamBuffer = 2097152            // 2MB 流缓冲区
	config.KeepAliveInterval = 10 * time.Second // 更频繁的心跳检测
	config.KeepAliveTimeout = 30 * time.Second  // 更短的超时时间以便快速检测连接问题
	config.MaxFrameSize = 32768                 // 32KB 帧大小
	config.KeepAliveDisabled = false            // 确保开启 keepalive

	session, err := smux.Server(conn, config)
	if err != nil {
		conn.Close()
		return nil
	}

	return &SafeConn{
		conn:    conn,
		session: session,
	}
}

// 关闭通道
func (c *SafeConn) Close() error {
	// 关闭smux会话
	if c.session != nil {
		c.session.Close()
	}
	return c.conn.Close()
}

// GetSession 获取smux会话
func (c *SafeConn) GetSession() *smux.Session {
	return c.session
}

type TunnelGroup struct {
	Conns  map[string]*SafeConn
	Locker sync.RWMutex
}

func GetTunnelGroup() *TunnelGroup {
	scgOnce.Do(func() {
		tunnelGroup = &TunnelGroup{
			Conns: make(map[string]*SafeConn),
		}
	})
	return tunnelGroup
}

func (g *TunnelGroup) AddTunnel(uuid string, conn *SafeConn) {
	g.Locker.Lock()
	defer g.Locker.Unlock()

	g.Conns[uuid] = conn
}

func (g *TunnelGroup) GetTunnel(uuid string) *SafeConn {
	g.Locker.RLock()
	defer g.Locker.RUnlock()
	if conn, ok := g.Conns[uuid]; ok {
		return conn
	}
	return nil
}

func (g *TunnelGroup) CloseTunnel(uuid string) {
	g.Locker.Lock()
	defer g.Locker.Unlock()
	if conn, ok := g.Conns[uuid]; ok {
		err := conn.Close()
		if err != nil {
			log.Printf("close tunnel error: %v", err)
		}

		delete(g.Conns, uuid)
	}
}
